/* Enhanced Navigation Styles for <PERSON><PERSON> Interface */

/* Main Navigation Styling */
.navbar-dark.bg-primary {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Add spacing between nav items */
.navbar-nav .nav-item {
    margin-right: 0.25rem;
}

/* Enhance dropdown menus */
.dropdown-menu {
    border-radius: 0.5rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    border: none;
    min-width: 240px;
}

/* Style dropdown headers */
.dropdown-header {
    font-weight: 600;
    color: #495057;
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Style dropdown items */
.dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.9rem;
}

.dropdown-item i {
    width: 20px;
    margin-right: 0.5rem;
    text-align: center;
    color: #6c757d;
}

.dropdown-item:hover i,
.dropdown-item:focus i {
    color: #fff;
}

/* Hover effects for dropdown items */
.dropdown-item:hover,
.dropdown-item:focus {
    background-color: #007bff;
    color: white;
}

/* Style dividers */
.dropdown-divider {
    margin: 0.25rem 0;
}

/* Badge styling */
.badge {
    font-weight: 500;
    padding: 0.25em 0.5em;
}

/* Quick Access Bar */
.quick-actions .btn {
    font-size: 0.85rem;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
}

.quick-actions .btn i {
    margin-right: 0.25rem;
}

/* Breadcrumb styling */
.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: #6c757d;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #007bff;
    text-decoration: underline;
}

.breadcrumb-item.active {
    color: #495057;
}

/* Notification indicator */
.nav-link .badge {
    font-size: 0.65rem;
}

/* Color coding for different sections */
/* Schedule - Blue */
#tutorScheduleDropdown i {
    color: #007bff;
}

/* Students - Green */
.nav-item .nav-link i.fa-user-graduate {
    color: #28a745;
}

/* Payments - Yellow */
.nav-item .nav-link i.fa-money-bill-wave {
    color: #ffc107;
}

/* Profile - Purple */
#profileDropdown i {
    color: #6f42c1;
}

/* Responsive adjustments */
@media (max-width: 992px) {
    .navbar-nav .nav-item {
        margin-right: 0;
    }
    
    .dropdown-menu {
        border: none;
        box-shadow: none;
        padding-left: 1rem;
        min-width: auto;
    }
    
    .quick-actions {
        display: flex;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .breadcrumb-wrapper {
        display: none;
    }
}

/* Highlight active navigation item */
.nav-item.active .nav-link,
.nav-item .nav-link.active {
    font-weight: 600;
    position: relative;
}

.nav-item.active .nav-link:after,
.nav-item .nav-link.active:after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0.5rem;
    right: 0.5rem;
    height: 3px;
    background-color: white;
    border-radius: 3px 3px 0 0;
}

/* User dropdown styling */
#userDropdown i {
    font-size: 1.2rem;
    margin-right: 0.25rem;
}

/* Language selector styling */
.language-btn {
    font-size: 0.8rem;
}

.language-btn.active {
    font-weight: 600;
}

/* Enhance mobile navigation */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .navbar-toggler {
        border: none;
        padding: 0.25rem 0.5rem;
    }
    
    .navbar-toggler:focus {
        box-shadow: none;
    }
    
    .navbar-collapse {
        max-height: 80vh;
        overflow-y: auto;
    }
}
