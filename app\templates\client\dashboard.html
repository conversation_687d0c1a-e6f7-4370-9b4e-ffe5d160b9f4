<!-- app/templates/client/dashboard.html -->
{% extends "base.html" %}

{% block title %}Client Dashboard - TutorAide Inc.{% endblock %}

{% block content %}
<!-- Welcome Section with Clean Styling -->
<div class="dashboard-welcome fade-in-up">
    <div class="row align-items-center">
        <div class="col-md-12">
            <h2 class="mb-3">{{ t('dashboard.welcome') }}, {{ client.first_name }}!</h2>
            <p class="text-muted">{{ t('dashboard.subtitle') }}</p>
        </div>
    </div>
</div>

<!-- Statistics Cards with Clean Design -->
<div class="row mb-4">
    <div class="col-md-3 mb-3">
        <div class="stat-card fade-in-up">
            <div class="stat-number">{{ upcoming_appointments|length }}</div>
            <div class="stat-label">{{ t('dashboard.upcoming_appointments') }}</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card fade-in-up" style="animation-delay: 0.1s;">
            <div class="stat-number">{{ unpaid_invoices|length }}</div>
            <div class="stat-label">{{ t('dashboard.pending_invoices') }}</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card fade-in-up" style="animation-delay: 0.2s;">
            <div class="stat-number">{{ active_subscriptions|length }}</div>
            <div class="stat-label">{{ t('dashboard.active_subscriptions') }}</div>
        </div>
    </div>
    <div class="col-md-3 mb-3">
        <div class="stat-card fade-in-up" style="animation-delay: 0.3s;">
            <div class="stat-number">{{ related_clients|length }}</div>
            <div class="stat-label">{{ t('dashboard.related_clients') }}</div>
        </div>
    </div>
</div>

<!-- Cards for Related Clients -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-light d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ t('clients.related.title') }}</h5>
                <a href="{{ url_for('client.add_related_client') }}" class="btn btn-sm btn-primary">
                    <i class="fas fa-plus"></i> {{ t('clients.related.add_new') }}
                </a>
            </div>
            <div class="card-body">
                {% if related_clients %}
                    <div class="row">
                        {% for related_client in related_clients %}
                            <div class="col-md-4 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ related_client.client.first_name }} {{ related_client.client.last_name }}</h5>
                                        <p class="card-text">
                                            <span class="badge bg-info">{{ t('clients.relationship.' + related_client.relationship_type) }}</span>
                                            {% if related_client.is_primary %}
                                                <span class="badge bg-success">{{ t('clients.primary') }}</span>
                                            {% endif %}
                                        </p>
                                        {% if related_client.client.individual_clients and related_client.client.individual_clients.date_of_birth %}
                                            <p class="card-text">{{ t('clients.age') }}: {{ related_client.client.individual_clients.age }}</p>
                                        {% endif %}
                                        <div class="mt-2">
                                            <a href="{{ url_for('client.view_related_client', id=related_client.client.id) }}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye"></i> {{ t('buttons.view') }}
                                            </a>
                                            <a href="{{ url_for('client.appointments', client_id=related_client.client.id) }}" class="btn btn-sm btn-outline-info">
                                                <i class="fas fa-calendar"></i> {{ t('buttons.schedule') }}
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-users fa-3x text-muted mb-3"></i>
                        <p>{{ t('clients.related.none') }}</p>
                        <a href="{{ url_for('client.add_related_client') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> {{ t('clients.related.add_first') }}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Upcoming Appointments -->
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card shadow h-100">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ t('appointments.upcoming') }}</h5>
                <a href="{{ url_for('client.appointments', status='upcoming') }}" class="btn btn-sm btn-light">
                    {{ t('buttons.view_all') }}
                </a>
            </div>
            <div class="card-body">
                {% if upcoming_appointments %}
                    <div class="list-group">
                        {% for appointment in upcoming_appointments %}
                            <a href="{{ url_for('client.view_appointment', id=appointment.id) }}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">
                                        {% if appointment.is_program_session and appointment.module_session %}
                                            {{ appointment.module_session.module_progress.module.program.name }} - 
                                            {{ appointment.module_session.module_progress.module.name }}
                                        {% else %}
                                            {{ appointment.tutor_service.service.name }}
                                        {% endif %}
                                    </h6>
                                    <small>{{ appointment.start_time.strftime('%b %d, %Y') }}</small>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <div>
                                        <p class="mb-1">
                                            <i class="fas fa-user-graduate me-1"></i> 
                                            {% if appointment.client %}
                                                {{ appointment.client.first_name }} {{ appointment.client.last_name }}
                                            {% endif %}
                                        </p>
                                        <p class="mb-1">
                                            <i class="fas fa-chalkboard-teacher me-1"></i> 
                                            {{ appointment.tutor.first_name }} {{ appointment.tutor.last_name }}
                                        </p>
                                    </div>
                                    <div>
                                        <span class="badge bg-primary">
                                            {{ appointment.start_time.strftime('%I:%M %p') }} - {{ appointment.end_time.strftime('%I:%M %p') }}
                                        </span>
                                    </div>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                        <p>{{ t('appointments.no_upcoming') }}</p>
                        <a href="{{ url_for('client.schedule_appointment') }}" class="btn btn-primary">
                            <i class="fas fa-plus"></i> {{ t('appointments.schedule_new') }}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>

    <!-- Unpaid Invoices -->
    <div class="col-md-6">
        <div class="card shadow h-100">
            <div class="card-header bg-warning text-dark d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ t('invoices.unpaid') }}</h5>
                <a href="{{ url_for('client.invoices', status='pending') }}" class="btn btn-sm btn-light">
                    {{ t('buttons.view_all') }}
                </a>
            </div>
            <div class="card-body">
                {% if unpaid_invoices %}
                    <div class="list-group">
                        {% for invoice in unpaid_invoices %}
                            <a href="{{ url_for('client.view_invoice', id=invoice.id) }}" class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1">{{ t('invoices.title') }}{{ invoice.id }}</h6>
                                    <small class="text-danger">{{ t('invoices.due') }}: {{ invoice.due_date.strftime('%b %d, %Y') }}</small>
                                </div>
                                <div class="d-flex justify-content-between align-items-center">
                                    <p class="mb-1">{{ invoice.items.count() }} {{ t('invoices.items') }}</p>
                                    <span class="badge bg-warning text-dark">${{ invoice.total_amount }}</span>
                                </div>
                            </a>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-file-invoice-dollar fa-3x text-muted mb-3"></i>
                        <p>{{ t('invoices.no_unpaid') }}</p>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Subscriptions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card shadow">
            <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0">{{ t('subscriptions.active') }}</h5>
                <a href="{{ url_for('client.subscriptions') }}" class="btn btn-sm btn-light">
                    {{ t('buttons.view_all') }}
                </a>
            </div>
            <div class="card-body">
                {% if active_subscriptions %}
                    <div class="row">
                        {% for subscription in active_subscriptions %}
                            <div class="col-md-6 mb-3">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <h5 class="card-title">{{ subscription.plan.name }}</h5>
                                        <p class="card-text">
                                            {{ t('subscriptions.valid_until') }}: {{ subscription.end_date.strftime('%b %d, %Y') }}
                                        </p>
                                        <div class="progress mb-3">
                                            <div class="progress-bar bg-success" role="progressbar" style="width: {{ subscription.usage_percentage }}%">
                                                {{ subscription.hours_used }} / {{ subscription.plan.max_hours }} {{ t('subscriptions.hours') }}
                                            </div>
                                        </div>
                                        <a href="{{ url_for('client.view_subscription', id=subscription.id) }}" class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-eye"></i> {{ t('buttons.view_details') }}
                                        </a>
                                    </div>
                                </div>
                            </div>
                        {% endfor %}
                    </div>
                {% else %}
                    <div class="text-center p-4">
                        <i class="fas fa-ticket-alt fa-3x text-muted mb-3"></i>
                        <p>{{ t('subscriptions.none_active') }}</p>
                        <a href="{{ url_for('client.subscription_plans') }}" class="btn btn-success">
                            <i class="fas fa-plus"></i> {{ t('subscriptions.get_subscription') }}
                        </a>
                    </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

{% if show_consent_modal %}
<!-- Consent Modal -->
<div class="modal fade" id="consentModal" tabindex="-1" aria-labelledby="consentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="consentModalLabel">{{ t('consent.title') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>{{ t('consent.intro') }}</p>
                
                <div class="mb-4">
                    <h6>{{ t('consent.mandatory_title') }}</h6>
                    <p>{{ t('consent.mandatory_text') }}</p>
                </div>
                
                <div class="mb-4">
                    <h6>{{ t('consent.optional_title') }}</h6>
                    <p>{{ t('consent.optional_text') }}</p>
                    
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="optionalConsent" 
                            {% if client_consent and client_consent.optional_consent %}checked{% endif %}>
                        <label class="form-check-label" for="optionalConsent">
                            {{ t('consent.optional_checkbox') }}
                        </label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <form id="consentForm" method="POST" action="{{ url_for('client.update_consent') }}">
                    <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                    <input type="hidden" name="optional_consent" id="optionalConsentValue" value="false">
                    <button type="submit" class="btn btn-primary">{{ t('consent.accept') }}</button>
                </form>
            </div>
        </div>
    </div>
</div>
{% endif %}
{% endblock %}

{% block scripts %}
<script>
    {% if show_consent_modal %}
    // Show consent modal on page load
    document.addEventListener('DOMContentLoaded', function() {
        var consentModal = new bootstrap.Modal(document.getElementById('consentModal'));
        consentModal.show();
        
        // Update hidden field when checkbox changes
        document.getElementById('optionalConsent').addEventListener('change', function() {
            document.getElementById('optionalConsentValue').value = this.checked;
        });
        
        // Set initial value
        document.getElementById('optionalConsentValue').value = 
            document.getElementById('optionalConsent').checked;
    });
    {% endif %}
</script>
{% endblock %}
