# app/utils/helpers.py
from datetime import datetime, time
from flask import current_app
import pytz

def format_currency(amount):
    """Format a numeric value as USD currency."""
    return "${:.2f}".format(float(amount))

def format_datetime(dt):
    """Format a datetime object for display."""
    if not dt:
        return ""
    return dt.strftime("%Y-%m-%d %I:%M %p")

def format_date(date):
    """Format a date object for display."""
    if not date:
        return ""
    return date.strftime("%Y-%m-%d")

def format_time(time_obj):
    """Format a time object for display."""
    if not time_obj:
        return ""
    return time_obj.strftime("%I:%M %p")

def validate_business_hours(start_time, end_time):
    """Validate that appointment times are within business hours (8 AM to 9 PM EST)."""
    if not start_time or not end_time:
        return False
    
    # Convert to EST timezone
    tz = pytz.timezone(current_app.config.get('TIMEZONE', 'America/New_York'))
    
    if isinstance(start_time, datetime):
        start_time = start_time.astimezone(tz).time()
    if isinstance(end_time, datetime):
        end_time = end_time.astimezone(tz).time()
    
    # Business hours: 8 AM to 9 PM
    business_start = time(8, 0)
    business_end = time(21, 0)
    
    # Check if times are within business hours
    if start_time < business_start or end_time > business_end:
        return False
    
    # Check if end time is after start time
    if start_time >= end_time:
        return False
    
    return True

def get_est_now():
    """Get current datetime in EST timezone."""
    tz = pytz.timezone(current_app.config.get('TIMEZONE', 'America/New_York'))
    return datetime.now(tz)