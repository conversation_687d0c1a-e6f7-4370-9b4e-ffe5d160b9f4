# app/views/client.py
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify, current_app, session
from flask_login import login_required, current_user
from app.extensions import db
from app.models.client import Client, IndividualClient, InstitutionalClient
from app.models.appointment import Appointment
from app.models.tutor import Tutor
from app.models.service import TutorService, Service
from app.models.invoice import Invoice, InvoiceItem
from app.forms.client_forms import ClientProfileForm
from app.services.invoice_service import InvoiceService
from app.services.consent_service import ConsentService
from app.models.subscription import Subscription, SubscriptionUsage
from app.models.program import Program, ProgramModule, ModuleProgress, ModuleSession, Enrollment, GroupSession, GroupSessionParticipant
from app.services.group_session_service import GroupSessionService
from app.i18n import t, get_locale, load_translations
from datetime import datetime, timedelta
from decimal import Decimal
import json
import stripe

client = Blueprint('client', __name__)

# Require client role for all routes in this blueprint
@client.before_request
def check_client():
    if not current_user.is_authenticated or current_user.role not in ['client', 'parent']:
        flash('You must be a client to access this area.', 'danger')
        return redirect(url_for('auth.login'))

@client.route('/')
@login_required
def dashboard():
    # Get the client profile
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    # Get upcoming appointments
    upcoming_appointments = Appointment.query.filter(
        Appointment.client_id == client.id,
        Appointment.start_time >= datetime.now(),
        Appointment.status == 'scheduled'
    ).order_by(Appointment.start_time).limit(5).all()

    # Get unpaid invoices
    all_invoices = InvoiceService.get_invoices_for_client(client.id)
    unpaid_invoices = [inv for inv in all_invoices if inv.status == 'pending']

    # Get past appointments
    past_appointments = Appointment.query.filter(
        Appointment.client_id == client.id,
        Appointment.start_time < datetime.now()
    ).order_by(Appointment.start_time.desc()).limit(5).all()

    # Get active subscriptions
    active_subscriptions = Subscription.query.filter(
        Subscription.client_id == client.id,
        Subscription.status == 'active',
        Subscription.end_date >= datetime.now().date()
    ).all()

    # Get active program enrollments
    active_enrollments = Enrollment.query.filter(
        Enrollment.client_id == client.id,
        Enrollment.status == 'active'
    ).all()

    # Get related clients (placeholder - empty list for now)
    related_clients = []

    # Check if the client has accepted the terms of service
    client_consent = ConsentService.get_client_consent(client.id)
    show_consent_modal = client_consent is None

    # Get the current locale for translations
    locale = get_locale()

    # If client has a preferred language and it's not the current locale, set it
    if client.preferred_language and client.preferred_language != locale:
        session['language'] = client.preferred_language
        locale = client.preferred_language

    return render_template('client/dashboard.html',
                          client=client,
                          upcoming_appointments=upcoming_appointments,
                          unpaid_invoices=unpaid_invoices,
                          past_appointments=past_appointments,
                          active_subscriptions=active_subscriptions,
                          active_enrollments=active_enrollments,
                          related_clients=related_clients,
                          show_consent_modal=show_consent_modal,
                          client_consent=client_consent,
                          t=t,
                          locale=locale,
                          load_translations=load_translations,
                          now=datetime.now())

@client.route('/profile', methods=['GET', 'POST'])
@login_required
def profile():
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    # Get client consent information for privacy settings
    client_consent = ConsentService.get_client_consent(client.id)

    form = ClientProfileForm(obj=client)
    if form.validate_on_submit():
        client.phone = form.phone.data
        client.address = form.address.data

        if form.password.data:
            current_user.set_password(form.password.data)

        db.session.commit()
        flash('Your profile has been updated.', 'success')
        return redirect(url_for('client.profile'))

    return render_template('client/profile.html',
                          form=form,
                          client=client,
                          client_consent=client_consent)

@client.route('/appointments')
@login_required
def appointments():
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    # Filter options
    status = request.args.get('status', 'upcoming')

    # Base query
    query = Appointment.query.filter(Appointment.client_id == client.id)

    # Apply filters
    if status == 'upcoming':
        query = query.filter(
            Appointment.start_time >= datetime.now(),
            Appointment.status == 'scheduled'
        )
    elif status == 'past':
        query = query.filter(
            Appointment.start_time < datetime.now()
        )
    elif status == 'cancelled':
        query = query.filter(
            Appointment.status == 'cancelled'
        )

    # Order the results
    if status == 'past':
        query = query.order_by(Appointment.start_time.desc())
    else:
        query = query.order_by(Appointment.start_time)

    all_appointments = query.all()

    # For the template tabs
    upcoming_appointments = [a for a in all_appointments if a.start_time >= datetime.now() and a.status == 'scheduled']
    past_appointments = [a for a in all_appointments if a.start_time < datetime.now()]

    # Get current time for the template
    now = datetime.now()

    return render_template('client/appointments.html',
                          all_appointments=all_appointments,
                          upcoming_appointments=upcoming_appointments,
                          past_appointments=past_appointments,
                          current_status=status,
                          now=now,
                          timedelta=timedelta)

@client.route('/appointments/<int:id>')
@login_required
def view_appointment(id):
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    # Find appointment and ensure it belongs to this client
    appointment = Appointment.query.filter(
        Appointment.id == id,
        Appointment.client_id == client.id
    ).first_or_404()

    # Get related objects
    tutor = Tutor.query.get(appointment.tutor_id)

    # Get service information
    tutor_service = TutorService.query.get(appointment.tutor_service_id)
    service = Service.query.get(tutor_service.service_id) if tutor_service else None

    # Calculate duration hours and total
    duration_hours = appointment.duration_minutes / 60
    total = 0
    if appointment.tutor_service:
        # Convert duration_hours to Decimal before multiplying
        from decimal import Decimal
        total = appointment.tutor_service.client_rate * Decimal(str(duration_hours))

    # Get current time for the template
    now = datetime.now()

    return render_template('client/appointment_detail.html',
                          appointment=appointment,
                          tutor=tutor,
                          service=service,
                          now=now,
                          duration_hours=duration_hours,
                          total=total,
                          Appointment=Appointment,  # Pass the Appointment model
                          timedelta=timedelta)

@client.route('/appointments/<int:id>/cancel', methods=['POST'])
@login_required
def cancel_appointment(id):
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    # Find appointment and ensure it belongs to this client
    appointment = Appointment.query.filter(
        Appointment.id == id,
        Appointment.client_id == client.id,
        Appointment.status == 'scheduled'
    ).first_or_404()

    # Check if appointment is more than 24 hours in the future
    if appointment.start_time <= datetime.now() + timedelta(hours=24):
        flash('Appointments must be cancelled at least 24 hours in advance.', 'danger')
        return redirect(url_for('client.view_appointment', id=id))

    # Cancel the appointment
    appointment.status = 'cancelled'
    db.session.commit()

    flash('Appointment cancelled successfully.', 'success')
    return redirect(url_for('client.appointments'))

@client.route('/invoices')
@login_required
def invoices():
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    # Filter options
    status = request.args.get('status', 'all')

    # Get all invoices for this client
    all_invoices = InvoiceService.get_invoices_for_client(client.id)

    # Apply filters
    if status == 'pending':
        invoices = [inv for inv in all_invoices if inv.status == 'pending']
    elif status == 'paid':
        invoices = [inv for inv in all_invoices if inv.status == 'paid']
    elif status == 'overdue':
        invoices = [inv for inv in all_invoices if inv.is_overdue]
    else:
        invoices = all_invoices

    # Sort by date descending
    invoices.sort(key=lambda x: x.invoice_date, reverse=True)

    # For the template tabs
    pending_invoices = [inv for inv in all_invoices if inv.status == 'pending']
    paid_invoices = [inv for inv in all_invoices if inv.status == 'paid']

    return render_template('client/invoices.html',
                          all_invoices=invoices,
                          pending_invoices=pending_invoices,
                          paid_invoices=paid_invoices,
                          current_status=status)

@client.route('/invoices/<int:id>')
@login_required
def view_invoice(id):
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    # Ensure invoice is accessible to this client
    invoice = Invoice.query.get_or_404(id)

    # Check if this client has access
    accessible_clients = invoice.get_accessible_clients()
    client_has_access = any(c.id == client.id for c in accessible_clients)

    if not client_has_access:
        flash('You do not have access to this invoice.', 'danger')
        return redirect(url_for('client.invoices'))

    # Get invoice items
    invoice_items = InvoiceItem.query.filter_by(invoice_id=invoice.id).all()

    # Get the client who paid if applicable
    paying_client = None
    if invoice.paid_by_client_id:
        paying_client = Client.query.get(invoice.paid_by_client_id)

    return render_template('client/invoice_detail.html',
                          invoice=invoice,
                          invoice_items=invoice_items,
                          paying_client=paying_client,
                          config=current_app.config)

@client.route('/invoices/<int:id>/pay', methods=['GET', 'POST'])
@login_required
def pay_invoice(id):
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    # Get the invoice
    invoice = Invoice.query.get_or_404(id)

    # Check if this client has access
    accessible_clients = invoice.get_accessible_clients()
    client_has_access = any(c.id == client.id for c in accessible_clients)

    if not client_has_access:
        flash('You do not have access to this invoice.', 'danger')
        return redirect(url_for('client.invoices'))

    # Ensure invoice is still pending payment
    if invoice.status != 'pending':
        flash('This invoice has already been paid.', 'info')
        return redirect(url_for('client.view_invoice', id=id))

    if request.method == 'POST':
        # Initialize Stripe with secret key
        stripe.api_key = current_app.config['STRIPE_SECRET_KEY']

        try:
            # Create payment intent with client ID
            payment_intent = InvoiceService.create_payment_intent(invoice.id, client.id)

            if not payment_intent:
                return jsonify({'error': 'Error creating payment intent. Please try again later.'}), 500

            return jsonify({'client_secret': payment_intent.client_secret})

        except stripe.error.StripeError as e:
            return jsonify({'error': str(e)}), 500

    return render_template('client/pay_invoice.html', invoice=invoice)

@client.route('/payment/success')
@login_required
def payment_success():
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()
    invoice_id = request.args.get('invoice_id', type=int)
    payment_intent_id = request.args.get('payment_intent', '')

    if payment_intent_id:
        # Process the successful payment with client ID
        success, invoice = InvoiceService.process_payment_success(payment_intent_id, client.id)

        if success and invoice:
            # Get current time for the template
            now = datetime.now()
            return render_template('client/payment_success.html', invoice=invoice, now=now)

    # Fall back to just showing success message
    return render_template('client/payment_success.html', invoice=None, now=datetime.now())

@client.route('/payment/cancelled')
@login_required
def payment_cancelled():
    invoice_id = request.args.get('invoice_id', type=int)

    if invoice_id:
        client = Client.query.filter_by(user_id=current_user.id).first_or_404()
        invoice = Invoice.query.filter_by(id=invoice_id, client_id=client.id).first()

        if invoice:
            return render_template('client/payment_cancelled.html', invoice=invoice)

    # Fall back to just showing cancelled message
    return render_template('client/payment_cancelled.html', invoice=None)

@client.route('/subscriptions')
@login_required
def subscriptions():
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    active_subscriptions = Subscription.query.filter(
        Subscription.client_id == client.id,
        Subscription.status == 'active',
        Subscription.end_date >= datetime.now().date()
    ).all()

    expired_subscriptions = Subscription.query.filter(
        Subscription.client_id == client.id,
        db.or_(
            Subscription.status != 'active',
            Subscription.end_date < datetime.now().date()
        )
    ).all()

    all_subscriptions = active_subscriptions + expired_subscriptions

    return render_template('client/subscriptions.html',
                          active_subscriptions=active_subscriptions,
                          expired_subscriptions=expired_subscriptions,
                          all_subscriptions=all_subscriptions)

@client.route('/subscriptions/<int:id>')
@login_required
def view_subscription(id):
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    # Ensure subscription belongs to this client
    subscription = Subscription.query.filter_by(id=id, client_id=client.id).first_or_404()

    # Get usage history
    usage_history = SubscriptionUsage.query.filter_by(subscription_id=subscription.id).order_by(SubscriptionUsage.insert_date.desc()).all()

    # Get upcoming appointments using this subscription
    subscription_appointments = Appointment.query.filter(
        Appointment.subscription_id == subscription.id
    ).order_by(Appointment.start_time).all()

    # Get invoices related to this subscription
    subscription_invoices = Invoice.query.filter(
        Invoice.subscription_id == subscription.id
    ).order_by(Invoice.invoice_date.desc()).all()

    # Get current time for the template
    now = datetime.now()

    return render_template('client/subscription_detail.html',
                          subscription=subscription,
                          usage_history=usage_history,
                          subscription_appointments=subscription_appointments,
                          subscription_invoices=subscription_invoices,
                          now=now)

@client.route('/save-consent', methods=['POST'])
@login_required
def save_consent():
    """Save client consent preferences."""
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    data = request.get_json()
    consent = data.get('consent', False)

    if consent:
        ConsentService.create_or_update_client_consent(client.id, "1.0", mandatory=True, optional=consent)
        return jsonify({'success': True})

    return jsonify({'success': False})

@client.route('/update-consent', methods=['POST'])
@login_required
def update_consent():
    """Update client consent preferences."""
    client = Client.query.filter_by(user_id=current_user.id).first_or_404()

    optional_consent = request.form.get('optional_consent') == 'true'

    ConsentService.create_or_update_client_consent(
        client.id,
        "1.0",
        mandatory=True,
        optional=optional_consent
    )

    flash('Your consent preferences have been updated.', 'success')
    return redirect(url_for('client.dashboard'))

@client.route('/add-related-client')
@login_required
def add_related_client():
    """Add a related client (placeholder route)."""
    flash('This feature is coming soon!', 'info')
    return redirect(url_for('client.dashboard'))

@client.route('/related-clients/<int:id>')
@login_required
def view_related_client(id):
    """View details of a related client (placeholder route)."""
    flash('This feature is coming soon!', 'info')
    return redirect(url_for('client.dashboard'))

@client.route('/schedule-appointment')
@login_required
def schedule_appointment():
    """Schedule a new appointment (placeholder route)."""
    flash('This feature is coming soon! Please contact us to schedule an appointment.', 'info')
    return redirect(url_for('client.dashboard'))

@client.route('/subscription-plans')
@login_required
def subscription_plans():
    """View available subscription plans (placeholder route)."""
    flash('This feature is coming soon!', 'info')
    return redirect(url_for('client.dashboard'))


@client.route('/tecfee-sessions')
@login_required
def tecfee_sessions():
    """View client's TECFÉE group sessions."""
    client_obj = Client.query.filter_by(user_id=current_user.id).first_or_404()

    # Get client's TECFÉE enrollment
    tecfee_program = GroupSessionService.get_tecfee_program()
    if not tecfee_program:
        flash('Programme TECFÉE non trouvé.', 'error')
        return redirect(url_for('client.dashboard'))

    enrollment = Enrollment.query.filter_by(
        client_id=client_obj.id,
        program_id=tecfee_program.id,
        status='active'
    ).first()

    if not enrollment:
        flash('Vous n\'êtes pas inscrit au programme TECFÉE.', 'warning')
        return redirect(url_for('client.dashboard'))

    # Get upcoming sessions for this client
    upcoming_sessions = GroupSessionService.get_upcoming_sessions_for_client(client_obj.id)

    # Get all sessions the client is enrolled in (including past ones)
    all_sessions = db.session.query(GroupSession, GroupSessionParticipant).\
        join(GroupSessionParticipant, GroupSession.id == GroupSessionParticipant.group_session_id).\
        filter(
            GroupSessionParticipant.enrollment_id == enrollment.id,
            GroupSession.program_id == tecfee_program.id
        ).\
        order_by(GroupSession.session_date.desc()).all()

    return render_template('client/tecfee_sessions.html',
                         enrollment=enrollment,
                         upcoming_sessions=upcoming_sessions,
                         all_sessions=all_sessions,
                         program=tecfee_program)
